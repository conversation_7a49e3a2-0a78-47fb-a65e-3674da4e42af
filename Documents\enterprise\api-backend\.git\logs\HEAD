0000000000000000000000000000000000000000 6d2c0149f439e10f7164679833ed12ef3cc9b1a3 <PERSON><PERSON><PERSON> <<EMAIL>> 1745513133 +0530	clone: from bitbucket.org:deviare/api-backend.git
6d2c0149f439e10f7164679833ed12ef3cc9b1a3 6fa676cd784ff2ecfca7ee037d32eddb082dcb43 <PERSON><PERSON><PERSON> <<EMAIL>> 1745514269 +0530	checkout: moving from master to staging
6fa676cd784ff2ecfca7ee037d32eddb082dcb43 6fa676cd784ff2ecfca7ee037d32eddb082dcb43 <PERSON><PERSON><PERSON> <<EMAIL>> 1745557303 +0530	checkout: moving from staging to updatetaskname
6fa676cd784ff2ecfca7ee037d32eddb082dcb43 c0bbe128a4f5b7388b881010a0a17477e78e3fe6 <PERSON><PERSON><PERSON> <<EMAIL>> 1745557325 +0530	commit: update taskname
c0bbe128a4f5b7388b881010a0a17477e78e3fe6 c0bbe128a4f5b7388b881010a0a17477e78e3fe6 Ashutosh Kumar <<EMAIL>> 1745671348 +0530	checkout: moving from updatetaskname to savesurveyresponsedb
c0bbe128a4f5b7388b881010a0a17477e78e3fe6 191d17fd120835c5f2221f190eb8962c74b7ff0a Ashutosh Kumar <<EMAIL>> 1745671402 +0530	commit: saving lime survey response in db.
191d17fd120835c5f2221f190eb8962c74b7ff0a 191d17fd120835c5f2221f190eb8962c74b7ff0a Ashutosh Kumar <<EMAIL>> 1745770743 +0530	checkout: moving from savesurveyresponsedb to updattaskfordbupdate
191d17fd120835c5f2221f190eb8962c74b7ff0a cbf349ddb1ae157d98842ce270204a934f384e97 Ashutosh Kumar <<EMAIL>> 1745770783 +0530	commit: update limesurvey & user report talnetlms
cbf349ddb1ae157d98842ce270204a934f384e97 cbf349ddb1ae157d98842ce270204a934f384e97 Ashutosh Kumar <<EMAIL>> 1745823131 +0530	reset: moving to HEAD
cbf349ddb1ae157d98842ce270204a934f384e97 cbf349ddb1ae157d98842ce270204a934f384e97 Ashutosh Kumar <<EMAIL>> 1745832924 +0530	reset: moving to HEAD
cbf349ddb1ae157d98842ce270204a934f384e97 cbf349ddb1ae157d98842ce270204a934f384e97 Ashutosh Kumar <<EMAIL>> 1745846002 +0530	checkout: moving from updattaskfordbupdate to updateCustomerAdminDashboard
cbf349ddb1ae157d98842ce270204a934f384e97 20b151a3785439ccd5ef0ffbfb842342d61ff14a Ashutosh Kumar <<EMAIL>> 1745846048 +0530	commit: update customer admin dashboard
20b151a3785439ccd5ef0ffbfb842342d61ff14a d35a1e650594fddacd03eeee187fdce7eee3caec Ashutosh Kumar <<EMAIL>> 1745858942 +0530	pull origin staging: Fast-forward
d35a1e650594fddacd03eeee187fdce7eee3caec d35a1e650594fddacd03eeee187fdce7eee3caec Ashutosh Kumar <<EMAIL>> 1745998173 +0530	checkout: moving from updateCustomerAdminDashboard to createApiforlimesurvey
d35a1e650594fddacd03eeee187fdce7eee3caec 8fd300a90a8e07dba7b6af812009573e318c506e Ashutosh Kumar <<EMAIL>> 1745998300 +0530	commit: create API for lime Survey
8fd300a90a8e07dba7b6af812009573e318c506e 48748b7f8409dfc66def119eec620b09d8cf0652 Ashutosh Kumar <<EMAIL>> 1746167276 +0530	pull origin staging: Fast-forward
48748b7f8409dfc66def119eec620b09d8cf0652 48748b7f8409dfc66def119eec620b09d8cf0652 Ashutosh Kumar <<EMAIL>> 1746700716 +0530	reset: moving to HEAD
48748b7f8409dfc66def119eec620b09d8cf0652 b36fde625acbb478b4966cf04fd8cd79aed0d77c Ashutosh Kumar <<EMAIL>> 1746700735 +0530	pull origin staging: Fast-forward
b36fde625acbb478b4966cf04fd8cd79aed0d77c cb1d14c1afbf5aac9b9a34457e0930cbf822e10f Ashutosh Kumar <<EMAIL>> 1746710098 +0530	commit: update survey name
cb1d14c1afbf5aac9b9a34457e0930cbf822e10f c20f63e285be87aba2c96291c7981d20c4c7da78 Ashutosh Kumar <<EMAIL>> 1746711472 +0530	commit: update survey names for assessment
c20f63e285be87aba2c96291c7981d20c4c7da78 c20f63e285be87aba2c96291c7981d20c4c7da78 Ashutosh Kumar <<EMAIL>> 1747029493 +0530	reset: moving to HEAD
c20f63e285be87aba2c96291c7981d20c4c7da78 1ad0c673f3040ee9b7ec01c5dc8f9df62a2ee43c Ashutosh Kumar <<EMAIL>> 1747120286 +0530	commit: update lime survey response
1ad0c673f3040ee9b7ec01c5dc8f9df62a2ee43c 42a351f5e8fab0c63c679cd19675d5c40abf197b Ashutosh Kumar <<EMAIL>> 1747124716 +0530	pull origin staging: Fast-forward
42a351f5e8fab0c63c679cd19675d5c40abf197b 42a351f5e8fab0c63c679cd19675d5c40abf197b Ashutosh Kumar <<EMAIL>> 1747131035 +0530	checkout: moving from createApiforlimesurvey to aiassesment
42a351f5e8fab0c63c679cd19675d5c40abf197b bbfa9bbe299ce9ba5a412ff32ea95d5b8c6f6f00 Ashutosh Kumar <<EMAIL>> 1747131056 +0530	commit: add ai assesment
bbfa9bbe299ce9ba5a412ff32ea95d5b8c6f6f00 0140c332b0b83777290bd7b2e87bde870ff5060e Ashutosh Kumar <<EMAIL>> 1747133756 +0530	commit: update aimw name
0140c332b0b83777290bd7b2e87bde870ff5060e 40ade320882ba7ca8b0a2c3ff14ffe027e65f9fb Ashutosh Kumar <<EMAIL>> 1747204516 +0530	commit: add pagination in UserSurveyAveragesView
40ade320882ba7ca8b0a2c3ff14ffe027e65f9fb 40ade320882ba7ca8b0a2c3ff14ffe027e65f9fb Ashutosh Kumar <<EMAIL>> 1747227971 +0530	checkout: moving from aiassesment to csmadmincount
40ade320882ba7ca8b0a2c3ff14ffe027e65f9fb 451e3537624417299454c594ceac34735af72064 Ashutosh Kumar <<EMAIL>> 1747228000 +0530	commit: update csmadmincountissue
451e3537624417299454c594ceac34735af72064 90a8f8f3d9998db7c65dd227cb9b81c1a57b9cf6 Ashutosh Kumar <<EMAIL>> 1747301985 +0530	commit: update usersurveyrsponse & add download-user-survey-averages
90a8f8f3d9998db7c65dd227cb9b81c1a57b9cf6 90a8f8f3d9998db7c65dd227cb9b81c1a57b9cf6 Ashutosh Kumar <<EMAIL>> 1747641304 +0530	reset: moving to HEAD
90a8f8f3d9998db7c65dd227cb9b81c1a57b9cf6 943f260ccbc9ddc7c3dfa31bb164de1c5366d597 Ashutosh Kumar <<EMAIL>> 1747650834 +0530	commit: update in API for lime survey report
943f260ccbc9ddc7c3dfa31bb164de1c5366d597 d006a59b80e29e026a33bb3790ddeebfd8b087ea Ashutosh Kumar <<EMAIL>> 1747653144 +0530	pull origin staging: Fast-forward
d006a59b80e29e026a33bb3790ddeebfd8b087ea 42a351f5e8fab0c63c679cd19675d5c40abf197b Ashutosh Kumar <<EMAIL>> 1747653158 +0530	checkout: moving from csmadmincount to createApiforlimesurvey
42a351f5e8fab0c63c679cd19675d5c40abf197b d006a59b80e29e026a33bb3790ddeebfd8b087ea Ashutosh Kumar <<EMAIL>> 1747653175 +0530	pull origin staging: Fast-forward
d006a59b80e29e026a33bb3790ddeebfd8b087ea 6fa676cd784ff2ecfca7ee037d32eddb082dcb43 Ashutosh Kumar <<EMAIL>> 1747653195 +0530	checkout: moving from createApiforlimesurvey to staging
6fa676cd784ff2ecfca7ee037d32eddb082dcb43 d006a59b80e29e026a33bb3790ddeebfd8b087ea Ashutosh Kumar <<EMAIL>> 1747653210 +0530	pull origin staging: Fast-forward
d006a59b80e29e026a33bb3790ddeebfd8b087ea d006a59b80e29e026a33bb3790ddeebfd8b087ea Ashutosh Kumar <<EMAIL>> 1747725165 +0530	checkout: moving from staging to assesmentname
d006a59b80e29e026a33bb3790ddeebfd8b087ea 6350c059581d3bc2f938616c5074d6735ba7a080 Ashutosh Kumar <<EMAIL>> 1747725208 +0530	commit: assesment name allocated dynamically
6350c059581d3bc2f938616c5074d6735ba7a080 6350c059581d3bc2f938616c5074d6735ba7a080 Ashutosh Kumar <<EMAIL>> 1747734486 +0530	reset: moving to HEAD
6350c059581d3bc2f938616c5074d6735ba7a080 c5bb3a2d5453bd078485e27b0ccd12cd97368c10 Ashutosh Kumar <<EMAIL>> 1747738236 +0530	commit: filtering bug fixed & reports download
c5bb3a2d5453bd078485e27b0ccd12cd97368c10 d006a59b80e29e026a33bb3790ddeebfd8b087ea Ashutosh Kumar <<EMAIL>> 1747739786 +0530	checkout: moving from assesmentname to staging
d006a59b80e29e026a33bb3790ddeebfd8b087ea 1e1d76ea335d5190e7f4653637f583e6c5ff669f Ashutosh Kumar <<EMAIL>> 1747739808 +0530	pull origin staging: Fast-forward
1e1d76ea335d5190e7f4653637f583e6c5ff669f 1e1d76ea335d5190e7f4653637f583e6c5ff669f Ashutosh Kumar <<EMAIL>> 1747828231 +0530	checkout: moving from staging to fixassessment
1e1d76ea335d5190e7f4653637f583e6c5ff669f 7d7e87771579ea142e05aeaf4f37be286eeada1f Ashutosh Kumar <<EMAIL>> 1747828268 +0530	commit: fix insight count for every role
7d7e87771579ea142e05aeaf4f37be286eeada1f ac9822865802481502f89452750ad9c6295ba5a0 Ashutosh Kumar <<EMAIL>> 1747829826 +0530	pull origin staging: Fast-forward
ac9822865802481502f89452750ad9c6295ba5a0 ac9822865802481502f89452750ad9c6295ba5a0 Ashutosh Kumar <<EMAIL>> 1748241320 +0530	checkout: moving from fixassessment to talentaccess
ac9822865802481502f89452750ad9c6295ba5a0 146166d96b7551fac7aaed978570ddc283718b68 Ashutosh Kumar <<EMAIL>> 1748241505 +0530	commit: added talent_access columns in company table
146166d96b7551fac7aaed978570ddc283718b68 23f2265ee7df000c6e38215507323c7496d9a2f5 Ashutosh Kumar <<EMAIL>> 1748321681 +0530	pull origin staging: Fast-forward
23f2265ee7df000c6e38215507323c7496d9a2f5 be4b934aa221ed42c330d7ba7bd76479a269dc85 Ashutosh Kumar <<EMAIL>> 1748321730 +0530	commit: added send API to users who completed the talelelms courses
be4b934aa221ed42c330d7ba7bd76479a269dc85 ea613fe6bc146b52fd50e0b225b95c24b5953848 Ashutosh Kumar <<EMAIL>> 1748359200 +0530	pull origin staging: Fast-forward
ea613fe6bc146b52fd50e0b225b95c24b5953848 ea613fe6bc146b52fd50e0b225b95c24b5953848 Ashutosh Kumar <<EMAIL>> 1748608132 +0530	checkout: moving from talentaccess to revinovaupdatecode
ea613fe6bc146b52fd50e0b225b95c24b5953848 445a0505468f14e4be0f6840aeef9f0de9e15a50 Ashutosh Kumar <<EMAIL>> 1748608369 +0530	commit: revinova celery task code has been optimized
445a0505468f14e4be0f6840aeef9f0de9e15a50 445a0505468f14e4be0f6840aeef9f0de9e15a50 Ashutosh Kumar <<EMAIL>> 1748867031 +0530	reset: moving to HEAD
445a0505468f14e4be0f6840aeef9f0de9e15a50 2f08c684ddcec80147b7c168237051fa5690cb5a Ashutosh Kumar <<EMAIL>> 1748867795 +0530	commit: company all user list which has assigned talentlms course
2f08c684ddcec80147b7c168237051fa5690cb5a d8d78c20433c0217f880b7c8846d3d6727d418c3 Ashutosh Kumar <<EMAIL>> 1748922388 +0530	pull origin staging: Fast-forward
d8d78c20433c0217f880b7c8846d3d6727d418c3 1e1d76ea335d5190e7f4653637f583e6c5ff669f Ashutosh Kumar <<EMAIL>> 1748922511 +0530	checkout: moving from revinovaupdatecode to staging
1e1d76ea335d5190e7f4653637f583e6c5ff669f d8d78c20433c0217f880b7c8846d3d6727d418c3 Ashutosh Kumar <<EMAIL>> 1748922525 +0530	pull origin staging: Fast-forward
d8d78c20433c0217f880b7c8846d3d6727d418c3 d8d78c20433c0217f880b7c8846d3d6727d418c3 Ashutosh Kumar <<EMAIL>> 1748945186 +0530	checkout: moving from staging to Revinovaupdatecode
d8d78c20433c0217f880b7c8846d3d6727d418c3 9092a2326e269891907db41e116f31af740b512c Ashutosh Kumar <<EMAIL>> 1748945259 +0530	commit: add revinova course in both CompanyTalentUsers & CompanyAllUsers Api
9092a2326e269891907db41e116f31af740b512c 2a48e989aeb251d3edfc0899bea3222d757f3c63 Ashutosh Kumar <<EMAIL>> 1748946592 +0530	pull origin staging: Fast-forward
2a48e989aeb251d3edfc0899bea3222d757f3c63 4c0ac0899388d8ead995727ce97a02d46e6f10be Ashutosh Kumar <<EMAIL>> 1748952072 +0530	commit: optimize revinova celery task code
4c0ac0899388d8ead995727ce97a02d46e6f10be be8bb624c6e44a250dd8de3e8090927ae26047f8 Ashutosh Kumar <<EMAIL>> 1748953597 +0530	pull origin staging: Fast-forward
be8bb624c6e44a250dd8de3e8090927ae26047f8 d8d78c20433c0217f880b7c8846d3d6727d418c3 Ashutosh Kumar <<EMAIL>> 1748953608 +0530	checkout: moving from Revinovaupdatecode to staging
d8d78c20433c0217f880b7c8846d3d6727d418c3 be8bb624c6e44a250dd8de3e8090927ae26047f8 Ashutosh Kumar <<EMAIL>> 1748953628 +0530	pull origin staging: Fast-forward
be8bb624c6e44a250dd8de3e8090927ae26047f8 be8bb624c6e44a250dd8de3e8090927ae26047f8 Ashutosh Kumar <<EMAIL>> 1749019097 +0530	checkout: moving from staging to Revinovaupdatecode
be8bb624c6e44a250dd8de3e8090927ae26047f8 c05006eefe4fe3cdf76738c923af99a62fa02af0 Ashutosh Kumar <<EMAIL>> 1749019129 +0530	commit: update revinova celery task code
c05006eefe4fe3cdf76738c923af99a62fa02af0 319807ded4a339cf8d9e20a5c18263ecff809c2c Ashutosh Kumar <<EMAIL>> 1749020230 +0530	pull origin staging: Fast-forward
319807ded4a339cf8d9e20a5c18263ecff809c2c be8bb624c6e44a250dd8de3e8090927ae26047f8 Ashutosh Kumar <<EMAIL>> 1749035492 +0530	checkout: moving from Revinovaupdatecode to staging
be8bb624c6e44a250dd8de3e8090927ae26047f8 319807ded4a339cf8d9e20a5c18263ecff809c2c Ashutosh Kumar <<EMAIL>> 1749035509 +0530	pull origin staging: Fast-forward
319807ded4a339cf8d9e20a5c18263ecff809c2c 319807ded4a339cf8d9e20a5c18263ecff809c2c Ashutosh Kumar <<EMAIL>> 1749041275 +0530	reset: moving to HEAD
319807ded4a339cf8d9e20a5c18263ecff809c2c 319807ded4a339cf8d9e20a5c18263ecff809c2c Ashutosh Kumar <<EMAIL>> 1749041287 +0530	checkout: moving from staging to Revinovaupdatecode
319807ded4a339cf8d9e20a5c18263ecff809c2c 5ab2d2dbb86040b5ed7dee37520cbe45743b7e1f Ashutosh Kumar <<EMAIL>> 1749041450 +0530	commit: revert revinova celery task code
5ab2d2dbb86040b5ed7dee37520cbe45743b7e1f 5ab2d2dbb86040b5ed7dee37520cbe45743b7e1f Ashutosh Kumar <<EMAIL>> 1749098424 +0530	reset: moving to HEAD
5ab2d2dbb86040b5ed7dee37520cbe45743b7e1f b968faea8175fde66296d65b0e062781d2d46c27 Ashutosh Kumar <<EMAIL>> 1749098442 +0530	pull origin staging: Fast-forward
b968faea8175fde66296d65b0e062781d2d46c27 319807ded4a339cf8d9e20a5c18263ecff809c2c Ashutosh Kumar <<EMAIL>> 1749098484 +0530	checkout: moving from Revinovaupdatecode to staging
319807ded4a339cf8d9e20a5c18263ecff809c2c b968faea8175fde66296d65b0e062781d2d46c27 Ashutosh Kumar <<EMAIL>> 1749098505 +0530	pull origin staging: Fast-forward
b968faea8175fde66296d65b0e062781d2d46c27 b968faea8175fde66296d65b0e062781d2d46c27 Ashutosh Kumar <<EMAIL>> 1749098540 +0530	checkout: moving from staging to Revinovaupdatecode
b968faea8175fde66296d65b0e062781d2d46c27 7764c432929ca67cecfeb895f00f424901bb1cfd Ashutosh Kumar <<EMAIL>> 1749100583 +0530	commit: updated revinova celery task code for coursecompletion
7764c432929ca67cecfeb895f00f424901bb1cfd b968faea8175fde66296d65b0e062781d2d46c27 Ashutosh Kumar <<EMAIL>> 1749101794 +0530	checkout: moving from Revinovaupdatecode to staging
b968faea8175fde66296d65b0e062781d2d46c27 d136ae37c439eb7eab13686819997abedfbe85c6 Ashutosh Kumar <<EMAIL>> 1749101809 +0530	pull origin staging: Fast-forward
d136ae37c439eb7eab13686819997abedfbe85c6 d136ae37c439eb7eab13686819997abedfbe85c6 Ashutosh Kumar <<EMAIL>> 1749235787 +0530	checkout: moving from staging to aiassesmentreport
d136ae37c439eb7eab13686819997abedfbe85c6 a3b6ce519c267afab6eb887513c48037adfbda5d Ashutosh Kumar <<EMAIL>> 1749235818 +0530	commit: add celery code for save ai assessment report
a3b6ce519c267afab6eb887513c48037adfbda5d d136ae37c439eb7eab13686819997abedfbe85c6 Ashutosh Kumar <<EMAIL>> 1749355690 +0530	checkout: moving from aiassesmentreport to staging
d136ae37c439eb7eab13686819997abedfbe85c6 91ea30667e435eb3028abcc434fcf572c71e0d20 Ashutosh Kumar <<EMAIL>> 1749355785 +0530	pull origin staging: Fast-forward
91ea30667e435eb3028abcc434fcf572c71e0d20 91ea30667e435eb3028abcc434fcf572c71e0d20 Ashutosh Kumar <<EMAIL>> 1749360324 +0530	checkout: moving from staging to updatetalentcode
91ea30667e435eb3028abcc434fcf572c71e0d20 163d4c310c00d560300f5c84565fadb2676555bf Ashutosh Kumar <<EMAIL>> 1749360369 +0530	commit: update celery talent report code
163d4c310c00d560300f5c84565fadb2676555bf 91ea30667e435eb3028abcc434fcf572c71e0d20 Ashutosh Kumar <<EMAIL>> 1749532251 +0530	checkout: moving from updatetalentcode to staging
91ea30667e435eb3028abcc434fcf572c71e0d20 1e5909e487fd55651b066a6a938205b34df8fb40 Ashutosh Kumar <<EMAIL>> 1749532295 +0530	pull origin staging: Fast-forward
1e5909e487fd55651b066a6a938205b34df8fb40 1e5909e487fd55651b066a6a938205b34df8fb40 Ashutosh Kumar <<EMAIL>> 1749537021 +0530	checkout: moving from staging to celerytime
1e5909e487fd55651b066a6a938205b34df8fb40 8f4095a424bd2e8558ac54dda2907e3bc7b49908 Ashutosh Kumar <<EMAIL>> 1749537060 +0530	commit: update celery time
8f4095a424bd2e8558ac54dda2907e3bc7b49908 17d21c02478b9739a13bb38b0e63b186e7f79da4 Ashutosh Kumar <<EMAIL>> 1749546762 +0530	pull origin staging: Fast-forward
17d21c02478b9739a13bb38b0e63b186e7f79da4 d444abc636e08a15fbc3e4f38d194e776111439c Ashutosh Kumar <<EMAIL>> 1749618525 +0530	commit: update enterprise user launch talent course
d444abc636e08a15fbc3e4f38d194e776111439c 1cbb1dd5af93f3ec6648f4546ecd57bf0a66cd0e Ashutosh Kumar <<EMAIL>> 1749624799 +0530	commit: update celery time in docker
1cbb1dd5af93f3ec6648f4546ecd57bf0a66cd0e 04f2ed6ac1b6e1c7e42aaeca94a804378c70b054 Ashutosh Kumar <<EMAIL>> 1749644085 +0530	commit: revert register user talentlms code
04f2ed6ac1b6e1c7e42aaeca94a804378c70b054 1e5909e487fd55651b066a6a938205b34df8fb40 Ashutosh Kumar <<EMAIL>> 1749795262 +0530	checkout: moving from celerytime to staging
1e5909e487fd55651b066a6a938205b34df8fb40 e105cf5a0a706b57cdab3e2827dced421766f7d3 Ashutosh Kumar <<EMAIL>> 1749795280 +0530	pull origin staging: Fast-forward
e105cf5a0a706b57cdab3e2827dced421766f7d3 e105cf5a0a706b57cdab3e2827dced421766f7d3 Ashutosh Kumar <<EMAIL>> 1750058650 +0530	checkout: moving from staging to badgefix
e105cf5a0a706b57cdab3e2827dced421766f7d3 40a50953e9f0e3349980865db45b48949af744fd Ashutosh Kumar <<EMAIL>> 1750058702 +0530	commit: badge fix.....
40a50953e9f0e3349980865db45b48949af744fd 40a50953e9f0e3349980865db45b48949af744fd Ashutosh Kumar <<EMAIL>> 1750095548 +0530	checkout: moving from badgefix to manualCeleryTrigger
40a50953e9f0e3349980865db45b48949af744fd 4f227d229175aa81dcc25e89f037dbf4a2579dff Ashutosh Kumar <<EMAIL>> 1750095732 +0530	commit: manually trigger task
4f227d229175aa81dcc25e89f037dbf4a2579dff f9916ccc768105d2a2ffa43f5a9913a41e7bcccb Ashutosh Kumar <<EMAIL>> 1750144665 +0530	commit: update manually trigger task
f9916ccc768105d2a2ffa43f5a9913a41e7bcccb 4398f00971681959b60846b4fe71e3931ef07c65 Ashutosh Kumar <<EMAIL>> 1750147166 +0530	pull origin staging: Fast-forward
4398f00971681959b60846b4fe71e3931ef07c65 e105cf5a0a706b57cdab3e2827dced421766f7d3 Ashutosh Kumar <<EMAIL>> 1750147179 +0530	checkout: moving from manualCeleryTrigger to staging
e105cf5a0a706b57cdab3e2827dced421766f7d3 4398f00971681959b60846b4fe71e3931ef07c65 Ashutosh Kumar <<EMAIL>> 1750147196 +0530	pull origin staging: Fast-forward
4398f00971681959b60846b4fe71e3931ef07c65 4398f00971681959b60846b4fe71e3931ef07c65 Ashutosh Kumar <<EMAIL>> 1750155483 +0530	checkout: moving from staging to manualCeleryTrigger
4398f00971681959b60846b4fe71e3931ef07c65 51554d8c55e57026fb53092c8e9b60b5b2024e52 Ashutosh Kumar <<EMAIL>> 1750155548 +0530	commit: update clear the db of celerytask
51554d8c55e57026fb53092c8e9b60b5b2024e52 07b16b36f57055f93bb7186b92b091b629a8866a Ashutosh Kumar <<EMAIL>> 1750157641 +0530	commit: update in docker file
07b16b36f57055f93bb7186b92b091b629a8866a 1549a600daff3bd493416f0ccdf3b371c6d82a30 Ashutosh Kumar <<EMAIL>> 1750221359 +0530	commit: fix cleanup_old_task_results task
1549a600daff3bd493416f0ccdf3b371c6d82a30 2fa2bcc427aee75f0d1838eefce211a2fd187374 Ashutosh Kumar <<EMAIL>> 1750672290 +0530	commit: manual triggered tasks fix
2fa2bcc427aee75f0d1838eefce211a2fd187374 2fa2bcc427aee75f0d1838eefce211a2fd187374 Ashutosh Kumar <<EMAIL>> 1750685690 +0530	reset: moving to HEAD
2fa2bcc427aee75f0d1838eefce211a2fd187374 280c5d56489388d36531669dce200af4e8ccb8df Ashutosh Kumar <<EMAIL>> 1750740023 +0530	pull origin staging: Fast-forward
280c5d56489388d36531669dce200af4e8ccb8df 280c5d56489388d36531669dce200af4e8ccb8df Ashutosh Kumar <<EMAIL>> 1750751546 +0530	checkout: moving from manualCeleryTrigger to asAssessmentResponse
280c5d56489388d36531669dce200af4e8ccb8df 864de70a72ccf7bf284ed16c53c63d89881bdb2c Ashutosh Kumar <<EMAIL>> 1750751585 +0530	commit: Ai assessment report api completed
864de70a72ccf7bf284ed16c53c63d89881bdb2c 9c8c0d7be3d0133e9b3474f30b72f544c2cd5e48 Ashutosh Kumar <<EMAIL>> 1750761730 +0530	commit: Ai assessment report api completed..
9c8c0d7be3d0133e9b3474f30b72f544c2cd5e48 9c8c0d7be3d0133e9b3474f30b72f544c2cd5e48 Ashutosh Kumar <<EMAIL>> 1751447871 +0530	checkout: moving from asAssessmentResponse to composer
9c8c0d7be3d0133e9b3474f30b72f544c2cd5e48 2f46e531b73be0a75312c3c864ecc529b32e561a Ashutosh Kumar <<EMAIL>> ********** +0530	commit: initial composer requirements done.
2f46e531b73be0a75312c3c864ecc529b32e561a e671e37a7fcdd713a4c854c20d75c47baceb1f3f Ashutosh Kumar <<EMAIL>> ********** +0530	commit: add user can launh the assigned composer.
e671e37a7fcdd713a4c854c20d75c47baceb1f3f 3b26f14137670f4b8453d40b741f5ab194a81b40 Ashutosh Kumar <<EMAIL>> ********** +0530	commit: added the composer license to customer.
3b26f14137670f4b8453d40b741f5ab194a81b40 4dc8fdda5b2c58ea4191dcbedbc7478617195fe8 Ashutosh Kumar <<EMAIL>> ********** +0530	commit: added the composer license to project
4dc8fdda5b2c58ea4191dcbedbc7478617195fe8 4dc8fdda5b2c58ea4191dcbedbc7478617195fe8 Ashutosh Kumar <<EMAIL>> ********** +0530	reset: moving to HEAD
4dc8fdda5b2c58ea4191dcbedbc7478617195fe8 3a409cb0452d1b3c5e8caabb9d962ecac7cb96ec Ashutosh Kumar <<EMAIL>> ********** +0530	pull origin staging: Fast-forward
3a409cb0452d1b3c5e8caabb9d962ecac7cb96ec e7dac734ac151ca1b94c0e58ec2eadb448213ea2 Ashutosh Kumar <<EMAIL>> ********** +0530	commit: assign customer composer completed
e7dac734ac151ca1b94c0e58ec2eadb448213ea2 b778d5b06486b56bc9bf674ffd33e22548802521 Ashutosh Kumar <<EMAIL>> ********** +0530	commit: completed
b778d5b06486b56bc9bf674ffd33e22548802521 3574fff9bdd1a7767596f9c21a2afa67b3b2d8a9 Ashutosh Kumar <<EMAIL>> ********** +0530	commit: add status = 0 revinova
3574fff9bdd1a7767596f9c21a2afa67b3b2d8a9 9dcd89b61b8f19a8b9e666c2c1eed4d12f43c01b Ashutosh Kumar <<EMAIL>> ********** +0530	pull origin staging: Fast-forward
9dcd89b61b8f19a8b9e666c2c1eed4d12f43c01b 4398f00971681959b60846b4fe71e3931ef07c65 Ashutosh Kumar <<EMAIL>> 1752056016 +0530	checkout: moving from composer to staging
4398f00971681959b60846b4fe71e3931ef07c65 9dcd89b61b8f19a8b9e666c2c1eed4d12f43c01b Ashutosh Kumar <<EMAIL>> 1752056037 +0530	pull origin staging: Fast-forward
9dcd89b61b8f19a8b9e666c2c1eed4d12f43c01b 9dcd89b61b8f19a8b9e666c2c1eed4d12f43c01b Ashutosh Kumar <<EMAIL>> 1752056226 +0530	checkout: moving from staging to composer
9dcd89b61b8f19a8b9e666c2c1eed4d12f43c01b 1e9266f044ac054c919ec3d52f6b495f67f8b555 Ashutosh Kumar <<EMAIL>> 1752285027 +0530	commit: update talent user report tasks
1e9266f044ac054c919ec3d52f6b495f67f8b555 1e9266f044ac054c919ec3d52f6b495f67f8b555 Ashutosh Kumar <<EMAIL>> ********** +0530	reset: moving to HEAD
1e9266f044ac054c919ec3d52f6b495f67f8b555 b8774ee1e2e2d884533ff5bcd29f4bce4a2445f0 Ashutosh Kumar <<EMAIL>> ********** +0530	commit: skip 100% completed users
b8774ee1e2e2d884533ff5bcd29f4bce4a2445f0 75fecefd6fbe70238f0769ab10ca80a3279db834 Ashutosh Kumar <<EMAIL>> ********** +0530	commit: staging migration for count(*) done
75fecefd6fbe70238f0769ab10ca80a3279db834 03fed7ada62954afcaa8cabdc44c49d7c6f05f9b Ashutosh Kumar <<EMAIL>> ********** +0530	commit: add courselicense count in talentlms report
03fed7ada62954afcaa8cabdc44c49d7c6f05f9b 5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d Ashutosh Kumar <<EMAIL>> ********** +0530	pull origin staging: Fast-forward
5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d 5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d Ashutosh Kumar <<EMAIL>> ********** +0530	reset: moving to HEAD
5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d 5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d Ashutosh Kumar <<EMAIL>> ********** +0530	reset: moving to HEAD
5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d 5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d Ashutosh Kumar <<EMAIL>> ********** +0530	reset: moving to HEAD
5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d 9dcd89b61b8f19a8b9e666c2c1eed4d12f43c01b Ashutosh Kumar <<EMAIL>> ********** +0530	checkout: moving from composer to staging
9dcd89b61b8f19a8b9e666c2c1eed4d12f43c01b 5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d Ashutosh Kumar <<EMAIL>> ********** +0530	pull origin staging: Fast-forward
5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d 5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d Ashutosh Kumar <<EMAIL>> 1752563116 +0530	reset: moving to HEAD
5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d 5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d Ashutosh Kumar <<EMAIL>> 1752582117 +0530	checkout: moving from staging to EcommerceTemp
5b4ed9d083da0d7ba429c3f7ce2ff8abf577d34d e27ee115eec7e7492acb5ab0cd7a958ca0492d3a Ashutosh Kumar <<EMAIL>> 1752582252 +0530	commit: till fetch list of category done
e27ee115eec7e7492acb5ab0cd7a958ca0492d3a 42fcd355ed024b171432c6bc24a6ad3722a6db05 Ashutosh Kumar <<EMAIL>> 1752654137 +0530	commit: get category by id done
42fcd355ed024b171432c6bc24a6ad3722a6db05 567f882d73e7ffb8167179f99972f9b41bc63ac5 Rahul Kumar <<EMAIL>> 1753447601 +0530	commit: course models update for ecommerce
567f882d73e7ffb8167179f99972f9b41bc63ac5 9493abbcd314cb5ef66cefb043ca9161ea158f60 Rahul Kumar <<EMAIL>> 1753724192 +0530	pull origin staging: Fast-forward
9493abbcd314cb5ef66cefb043ca9161ea158f60 9493abbcd314cb5ef66cefb043ca9161ea158f60 Rahul Kumar <<EMAIL>> 1753724229 +0530	checkout: moving from EcommerceTemp to EmailDomainChange
9493abbcd314cb5ef66cefb043ca9161ea158f60 49c028dc45a5ef83ca9cc4ca0a10a1fdae15ca98 Rahul Kumar <<EMAIL>> 1753724384 +0530	commit: change all email co.za to .africa
49c028dc45a5ef83ca9cc4ca0a10a1fdae15ca98 9493abbcd314cb5ef66cefb043ca9161ea158f60 Rahul Kumar <<EMAIL>> 1753724528 +0530	checkout: moving from EmailDomainChange to EcommerceTemp
9493abbcd314cb5ef66cefb043ca9161ea158f60 d0ba07d4b20a8aa66d19aa29377416951ade89cb Rahul Kumar <<EMAIL>> 1753724544 +0530	pull origin staging: Fast-forward
d0ba07d4b20a8aa66d19aa29377416951ade89cb 49c028dc45a5ef83ca9cc4ca0a10a1fdae15ca98 Rahul Kumar <<EMAIL>> 1753770752 +0530	checkout: moving from EcommerceTemp to EmailDomainChange
49c028dc45a5ef83ca9cc4ca0a10a1fdae15ca98 d0ba07d4b20a8aa66d19aa29377416951ade89cb Rahul Kumar <<EMAIL>> 1753770767 +0530	pull origin staging: Fast-forward
d0ba07d4b20a8aa66d19aa29377416951ade89cb 0b4828a0cf75019c696f48b59307b5dcd669d71d Rahul Kumar <<EMAIL>> 1753771889 +0530	commit: assesment email updated
0b4828a0cf75019c696f48b59307b5dcd669d71d d0ba07d4b20a8aa66d19aa29377416951ade89cb Rahul Kumar <<EMAIL>> 1753773768 +0530	checkout: moving from EmailDomainChange to EcommerceTemp
d0ba07d4b20a8aa66d19aa29377416951ade89cb 5fc56cf47d05292990c80f6348998c1b6288d8ef Rahul Kumar <<EMAIL>> 1753773795 +0530	pull origin staging: Fast-forward
5fc56cf47d05292990c80f6348998c1b6288d8ef 5e1e7579375400742137c3d0d60715d23684f46a Rahul Kumar <<EMAIL>> 1755085452 +0530	commit: inintial setup and API created
5e1e7579375400742137c3d0d60715d23684f46a 54b498986198999dbc819a1251adf57a956e32e3 Rahul Kumar <<EMAIL>> 1755602777 +0530	pull origin staging: Fast-forward
54b498986198999dbc819a1251adf57a956e32e3 73a8f49c57a22c4313b23e45c0a476a44d75d51f Rahul Kumar <<EMAIL>> 1755602841 +0530	commit: db synced & celery part done
73a8f49c57a22c4313b23e45c0a476a44d75d51f 667ed7b94534753a4f833df7e54ef0abcd69ff02 Rahul Kumar <<EMAIL>> 1755755221 +0530	commit: Add search functionality to CourseDropdownsAndListAPIView
667ed7b94534753a4f833df7e54ef0abcd69ff02 24a45009cda77c1c2c39c557a41c08cab8e9ec26 Rahul Kumar <<EMAIL>> 1755759530 +0530	pull origin staging: Fast-forward
24a45009cda77c1c2c39c557a41c08cab8e9ec26 00193cff99a09168159bc8f07aba683e02f3201c Rahul Kumar <<EMAIL>> 1755858024 +0530	commit: update course detailed response
00193cff99a09168159bc8f07aba683e02f3201c 03ce88eaec30adc3a958a1631d993b7505fe453d Rahul Kumar <<EMAIL>> 1755865833 +0530	commit: updated course detailed responses
03ce88eaec30adc3a958a1631d993b7505fe453d 9b87415b96a9212fbb1ad4b5bb4b765f934153ce Rahul Kumar <<EMAIL>> 1756102250 +0530	commit: updated course detailed responses...
9b87415b96a9212fbb1ad4b5bb4b765f934153ce 3e4917fb2503172a4ea54aaa14af06ef312b8633 Rahul Kumar <<EMAIL>> 1756106889 +0530	commit: updated course detailed repsonse remove , add fixed pattern
3e4917fb2503172a4ea54aaa14af06ef312b8633 bb30cb571ac0f231be96d4a6f0e25d106141f4d1 Rahul Kumar <<EMAIL>> 1756108560 +0530	pull origin staging: Fast-forward
bb30cb571ac0f231be96d4a6f0e25d106141f4d1 9bb190a327378e9a3e22223d3a9041ff3a2158ea Rahul Kumar <<EMAIL>> 1756183833 +0530	commit: updated course detailed repsonse select top3
9bb190a327378e9a3e22223d3a9041ff3a2158ea 86d2a7755b70a4e3a89a510105207452d35cdab7 Rahul Kumar <<EMAIL>> 1756294062 +0530	commit: create SubscriptionFlowWebhook api
86d2a7755b70a4e3a89a510105207452d35cdab7 3bff5e73481f8ebda2697043e8fa416d3da3042b Rahul Kumar <<EMAIL>> 1756298636 +0530	commit: update bulkupload API
3bff5e73481f8ebda2697043e8fa416d3da3042b 1e73d4ab2f0dfe2f958d178ccfd8a7fd8fb3e4db Rahul Kumar <<EMAIL>> 1756314563 +0530	pull origin staging: Fast-forward
