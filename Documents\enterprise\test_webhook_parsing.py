#!/usr/bin/env python3
"""
Test script to verify webhook URL-encoded parsing logic
"""

from urllib.parse import parse_qs, unquote

def test_webhook_parsing():
    """Test the URL-encoded webhook parsing logic"""
    
    # Sample URL-encoded data from your webhook
    sample_data = "type=transactions&id=5717037d-b280-4b50-89ef-e6bb84788725&attributes%5Bid%5D=5717037d-b280-4b50-89ef-e6bb84788725&attributes%5Bname%5D=P-24&attributes%5Bdate%5D=2025-08-27T09%3A17%3A00-04%3A00&attributes%5Bstatus%5D=Pending&attributes%5Bamount%5D=1089&attributes%5Bmiscellaneous_charges%5D=0&attributes%5Bmiscellaneous_charges_breakdown%5D=&attributes%5Bunapplied_amount%5D=0&attributes%5Bbalance%5D=0&attributes%5Bnumber%5D=N%2FA&attributes%5Bpayment_type_id%5D=N%2FA&attributes%5Bpayment_method_id%5D=4828a5fe-04e9-4163-9306-f539ec6d97cc&attributes%5Bcash_or_card%5D=Card&attributes%5Btype%5D=Transaction&attributes%5Btransaction_category%5D=Payment&attributes%5Breference%5D=&attributes%5Bdescription%5D=Amazon+Transcribe+Getting+Started+-%3E+Annual+-+Basic&attributes%5Btransaction_id%5D=****************&attributes%5Breference_transaction_id%5D=&attributes%5Bdecline_reason%5D=&attributes%5Bcurrency%5D=ZAR&attributes%5Baccounting_account_code%5D=&attributes%5Bcreated_at%5D=2025-08-27T09%3A17%3A00-04%3A00&attributes%5Bupdated_at%5D=2025-08-27T09%3A17%3A00-04%3A00&attributes%5Bdata_source%5D=SubscriptionFlow%28HPP%29&attributes%5Breason_code%5D=&attributes%5Bmv_early_renewal%5D=0&attributes%5Bmv_renewal_plan%5D=&attributes%5Bapproval_link%5D=https%3A%2F%2Fdeviare.subscriptionflow.com%2Fen%2FPayment%2FProcess%3Ftransaction%3D5717037d-b280-4b50-89ef-e6bb84788725&attributes%5Bcustomer%5D%5Bid%5D=b3b7a5d7-88ff-49ad-9af6-e2f2026b1711&attributes%5Bcustomer%5D%5Bname%5D=test+payment&attributes%5Bcustomer%5D%5Bemail%5D=kumar.rahul%40kibalabs.in&attributes%5Bcustomer%5D%5Bcompany%5D=&attributes%5Bcustomer%5D%5Bphone_number%5D=%2B91+98272+63728&attributes%5Bcustomer%5D%5Bpo_number%5D=&attributes%5Bcustomer%5D%5Blegal_entity%5D=&attributes%5Bcustomer%5D%5Bcurrency%5D=ZAR&attributes%5Bcustomer%5D%5Bbilling_cycle_day%5D=&attributes%5Bcustomer%5D%5Bnotes%5D=&attributes%5Bcustomer%5D%5Bimage%5D=&attributes%5Bcustomer%5D%5Bcustomer_number%5D=CUST_265&attributes%5Bcustomer%5D%5Bterms%5D=&attributes%5Bcustomer%5D%5Bcreated_at%5D=2025-08-27T09%3A17%3A00-04%3A00&attributes%5Bcustomer%5D%5Bupdated_at%5D=2025-08-27T09%3A17%3A00-04%3A00&attributes%5Bcustomer%5D%5Bportal_is_enabled%5D=0&attributes%5Bcustomer%5D%5Btax_exempt%5D=0&attributes%5Bcustomer%5D%5Btax_exempt_code%5D=&attributes%5Bcustomer%5D%5Bcertificate_id%5D=&attributes%5Bcustomer%5D%5Bcertificate_type%5D=&attributes%5Bcustomer%5D%5Bissuing_jurisdiction%5D=&attributes%5Bcustomer%5D%5Bentity_use_code%5D=&attributes%5Bcustomer%5D%5Bdescription%5D=&attributes%5Bcustomer%5D%5Bvat_id%5D=&attributes%5Bcustomer%5D%5Blast_activity%5D=%7B%7BLBL_GENERATED_INVOICE%7D%7D+%3Ca+href%3D%22https%3A%2F%2Fdeviare.subscriptionflow.com%2Fen%2Finvoice-detail%2Fa4e8198d-d89e-425a-abd9-c84fa1d2e2f3%22%3EIN-27%3C%2Fa%3E&attributes%5Bcustomer%5D%5Bauto_charge%5D=1&attributes%5Bcustomer%5D%5Bbilling_address_1%5D=patna&attributes%5Bcustomer%5D%5Bbilling_address_2%5D=&attributes%5Bcustomer%5D%5Bbilling_address_3%5D=&attributes%5Bcustomer%5D%5Bbilling_city%5D=&attributes%5Bcustomer%5D%5Bbilling_state%5D=&attributes%5Bcustomer%5D%5Bbilling_county%5D=&attributes%5Bcustomer%5D%5Bbilling_postal_code%5D=&attributes%5Bcustomer%5D%5Bbilling_country%5D=IN&attributes%5Bcustomer%5D%5Bbilling_name%5D=&attributes%5Bcustomer%5D%5Bbilling_email%5D=&attributes%5Bcustomer%5D%5Bshipping_address_1%5D=patna&attributes%5Bcustomer%5D%5Bshipping_address_2%5D=&attributes%5Bcustomer%5D%5Bshipping_address_3%5D=&attributes%5Bcustomer%5D%5Bshipping_city%5D=&attributes%5Bcustomer%5D%5Bshipping_state%5D=&attributes%5Bcustomer%5D%5Bshipping_county%5D=&attributes%5Bcustomer%5D%5Bshipping_postal_code%5D=&attributes%5Bcustomer%5D%5Bshipping_country%5D=IN&attributes%5Bcustomer%5D%5Bshipping_name%5D=&attributes%5Bcustomer%5D%5Bshipping_email%5D=&attributes%5Bcustomer%5D%5Bdata_source%5D=SubscriptionFlow%28HPP%29&attributes%5Bcustomer%5D%5Bprimary_churn_score_value%5D=&attributes%5Bcustomer%5D%5Bprimary_churn_score_grade%5D=&attributes%5Bcustomer%5D%5Bmiscellaneous_charges_exempt%5D=&attributes%5Bcustomer%5D%5Binvoice_payer%5D=&attributes%5Bcustomer%5D%5Bconsolidate_invoices%5D=&attributes%5Bcustomer%5D%5Bprimary_recipient%5D=&attributes%5Bcustomer%5D%5Btags%5D=&attributes%5Binvoices%5D%5B0%5D%5Binvoice_id%5D=a4e8198d-d89e-425a-abd9-c84fa1d2e2f3&attributes%5Binvoices%5D%5B0%5D%5Bamount%5D=1089&attributes%5Binvoices%5D%5B0%5D%5Btransaction_status%5D=&relationships%5Bcustomer_id%5D%5Bid%5D=b3b7a5d7-88ff-49ad-9af6-e2f2026b1711&relationships%5Bcustomer_id%5D%5Blink%5D=http%3A%2F%2Flocalhost%2Fsubscriptionflow.com%2Fapi%2Fv1%2Fcustomers%2Fb3b7a5d7-88ff-49ad-9af6-e2f2026b1711&relationships%5Bcreated_by%5D%5Bid%5D=adb17978-041c-40de-9be1-4f11283eaacc&relationships%5Bcreated_by%5D%5Blink%5D=http%3A%2F%2Flocalhost%2Fsubscriptionflow.com%2Fapi%2Fv1%2Fusers%2Fadb17978-041c-40de-9be1-4f11283eaacc&relationships%5Bupdated_by%5D%5Bid%5D=adb17978-041c-40de-9be1-4f11283eaacc&relationships%5Bupdated_by%5D%5Blink%5D=http%3A%2F%2Flocalhost%2Fsubscriptionflow.com%2Fapi%2Fv1%2Fusers%2Fadb17978-041c-40de-9be1-4f11283eaacc&relationships%5Bassigned_to%5D%5Bid%5D=adb17978-041c-40de-9be1-4f11283eaacc&relationships%5Bassigned_to%5D%5Blink%5D=http%3A%2F%2Flocalhost%2Fsubscriptionflow.com%2Fapi%2Fv1%2Fusers%2Fadb17978-041c-40de-9be1-4f11283eaacc&event=created&method=POST"
    
    print("Testing URL-encoded webhook parsing...")
    print("=" * 50)
    
    # Parse the URL-encoded data
    parsed_data = parse_qs(sample_data)
    
    # Extract basic webhook info
    webhook_type = parsed_data.get('type', ['unknown'])[0]
    webhook_event = parsed_data.get('event', ['unknown'])[0] if 'event' in parsed_data else 'created'
    webhook_method = parsed_data.get('method', ['POST'])[0]
    
    print(f"Webhook Type: {webhook_type}")
    print(f"Webhook Event: {webhook_event}")
    print(f"Webhook Method: {webhook_method}")
    print()
    
    # Build attributes from parsed data
    attributes_data = {}
    for key, value_list in parsed_data.items():
        if key.startswith('attributes[') and key.endswith(']'):
            # Extract the field name from attributes[field_name]
            field_name = key[11:-1]  # Remove 'attributes[' and ']'

            # Handle nested fields like attributes[customer][id] or attributes[invoices][0][invoice_id]
            if '[' in field_name and ']' in field_name:
                # Parse nested structure
                parts = field_name.replace('][', '|').replace('[', '|').replace(']', '').split('|')
                current = attributes_data

                # Handle array structures like invoices[0][invoice_id]
                for i, part in enumerate(parts[:-1]):
                    if part.isdigit():
                        # This is an array index
                        array_name = parts[i-1] if i > 0 else 'items'
                        if array_name not in current:
                            current[array_name] = []

                        # Ensure the array has enough elements
                        index = int(part)
                        while len(current[array_name]) <= index:
                            current[array_name].append({})

                        current = current[array_name][index]
                    else:
                        # Regular nested object
                        if part not in current:
                            current[part] = {}
                        current = current[part]

                current[parts[-1]] = unquote(value_list[0]) if value_list else ''
            else:
                # Simple field
                attributes_data[field_name] = unquote(value_list[0]) if value_list else ''
    
    print("Parsed Transaction Attributes:")
    print("-" * 30)
    for key, value in attributes_data.items():
        if key != 'customer' and key != 'invoices':  # Skip nested objects for now
            print(f"{key}: {value}")
    
    print("\nCustomer Data:")
    print("-" * 15)
    if 'customer' in attributes_data:
        for key, value in attributes_data['customer'].items():
            print(f"{key}: {value}")
    
    print("\nInvoices Data:")
    print("-" * 15)
    if 'invoices' in attributes_data:
        print(f"invoices: {attributes_data['invoices']}")
    
    print("\n" + "=" * 50)
    print("Parsing completed successfully!")
    
    return attributes_data

if __name__ == "__main__":
    test_webhook_parsing()
